#!/usr/bin/env bun
/**
 * Seed Data Script for DOAXVV Handbook
 * 
 * This script populates the database with sample data for testing
 */

import { config } from 'dotenv';
config();

import { open, Database } from 'sqlite';
import sqlite3 from 'sqlite3';
import { join } from 'path';
import logger from '../config/logger';

async function seedData(): Promise<void> {
  let db: Database<sqlite3.Database, sqlite3.Statement> | null = null;
  
  try {
    logger.info('🌱 Starting data seeding...');
    
    // Connect to database
    const dbPath = join(__dirname, '../data/doaxvv_handbook.db');
    db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });
    
    logger.info('✅ Connected to SQLite database');
    
    // Seed Characters
    logger.info('📝 Seeding characters...');
    await db.run(`
      INSERT OR IGNORE INTO characters (id, name, name_jp, name_en, rarity, birthday, height, bust, waist, hip, hobby, favorite_food, description, avatar_image, voice_actor) VALUES
      ('kasu<PERSON>', '<PERSON><PERSON><PERSON>', 'かすみ', 'Kasu<PERSON>', '<PERSON>', '1990-02-23', 157, 95, 54, 83, 'Cooking', '<PERSON>rawberry shortcake', 'A cheerful and energetic girl who loves to help others.', '/images/characters/kasumi.jpg', 'Yui Horie'),
      ('ayane', 'Ayane', 'あやね', 'Ayane', 'SSR', '1991-07-27', 157, 99, 53, 84, '<PERSON> telling', 'Peach', 'A mysterious ninja with a complex past.', '/images/characters/ayane.jpg', 'Wakana Yamazaki'),
      ('hitomi', 'Hitomi', 'ひとみ', 'Hitomi', 'SR', '1990-08-12', 160, 90, 57, 86, 'Karate', 'Cherry pie', 'A German-Japanese karate expert with a strong sense of justice.', '/images/characters/hitomi.jpg', 'Yuka Koyama'),
      ('marie_rose', 'Marie Rose', 'マリー・ローズ', 'Marie Rose', 'SSR', '1992-12-25', 147, 74, 54, 79, 'Reading', 'Macarons', 'A young Swedish girl with a love for gothic lolita fashion.', '/images/characters/marie_rose.jpg', 'Mai Aizawa'),
      ('honoka', 'Honoka', 'ほのか', 'Honoka', 'SR', '1989-03-15', 166, 99, 59, 87, 'Cooking', 'Donuts', 'A cheerful college student who works part-time at her grandmother''s cafe.', '/images/characters/honoka.jpg', 'Ai Nonaka')
    `);
    
    // Seed Skills
    logger.info('📝 Seeding skills...');
    await db.run(`
      INSERT OR IGNORE INTO skills (id, name, type, category, description, icon, max_level, effect_type, effect_value, cooldown, duration) VALUES
      ('power_spike', 'Power Spike', 'offensive', 'power', 'Increases POW stat temporarily', '/icons/power_spike.png', 5, 'stat_boost', 15.0, 30, 60),
      ('tech_boost', 'Tech Boost', 'technical', 'technique', 'Increases TEC stat temporarily', '/icons/tech_boost.png', 5, 'stat_boost', 15.0, 30, 60),
      ('stamina_rush', 'Stamina Rush', 'support', 'stamina', 'Increases STM stat temporarily', '/icons/stamina_rush.png', 5, 'stat_boost', 15.0, 30, 60),
      ('appeal_charm', 'Appeal Charm', 'appeal', 'appeal', 'Increases APL stat temporarily', '/icons/appeal_charm.png', 5, 'stat_boost', 15.0, 30, 60),
      ('balanced_form', 'Balanced Form', 'balanced', 'general', 'Increases all stats moderately', '/icons/balanced_form.png', 3, 'stat_boost', 8.0, 45, 90)
    `);
    
    // Seed Swimsuits
    logger.info('📝 Seeding swimsuits...');
    await db.run(`
      INSERT OR IGNORE INTO swimsuits (id, name, character_id, rarity, type, pow, tec, stm, apl, release_date, image, gacha_type, is_limited, trend_type, max_level, description) VALUES
      ('kasumi_venus', 'Venus Kasumi', 'kasumi', 'SSR', 'swimsuit', 8500, 7200, 6800, 9100, '2024-01-15', '/images/swimsuits/kasumi_venus.jpg', 'premium', 1, 'apl', 80, 'A stunning Venus-themed swimsuit that enhances appeal.'),
      ('ayane_ninja', 'Ninja Ayane', 'ayane', 'SSR', 'outfit', 9200, 8800, 7500, 6900, '2024-02-01', '/images/swimsuits/ayane_ninja.jpg', 'event', 1, 'pow', 80, 'A sleek ninja outfit perfect for stealth and power.'),
      ('hitomi_karate', 'Karate Hitomi', 'hitomi', 'SR', 'outfit', 7800, 8200, 8500, 6200, '2024-01-20', '/images/swimsuits/hitomi_karate.jpg', 'standard', 0, 'stm', 70, 'Traditional karate gi adapted for beach activities.'),
      ('marie_gothic', 'Gothic Marie', 'marie_rose', 'SSR', 'dress', 6800, 9100, 7200, 8800, '2024-02-14', '/images/swimsuits/marie_gothic.jpg', 'premium', 1, 'tec', 80, 'An elegant gothic lolita dress with technical prowess.'),
      ('honoka_cafe', 'Cafe Honoka', 'honoka', 'SR', 'casual', 7500, 6800, 7800, 8200, '2024-01-30', '/images/swimsuits/honoka_cafe.jpg', 'standard', 0, 'apl', 70, 'A cute cafe uniform perfect for serving customers.')
    `);
    
    // Seed Shop Items
    logger.info('📝 Seeding shop items...');
    await db.run(`
      INSERT OR IGNORE INTO shop_items (id, name, description, type, category, section, price, currency, rarity, image, in_stock, is_new, discount, limited_time, featured) VALUES
      ('premium_gacha_ticket', 'Premium Gacha Ticket', 'A special ticket for premium gacha pulls', 'currency', 'Gacha', 'owner', 500, 'gems', 'epic', '/images/items/premium_ticket.png', 1, 1, 0, 0, 1),
      ('stamina_drink', 'Stamina Drink', 'Restores stamina for activities', 'booster', 'Consumables', 'owner', 100, 'coins', 'common', '/images/items/stamina_drink.png', 1, 0, 0, 0, 0),
      ('beach_umbrella', 'Beach Umbrella', 'Decorative umbrella for your private beach', 'decoration', 'Beach', 'owner', 2000, 'coins', 'rare', '/images/items/beach_umbrella.png', 1, 0, 15, 0, 0),
      ('event_ticket', 'Event Ticket', 'Special ticket for limited events', 'currency', 'Events', 'event', 300, 'tickets', 'legendary', '/images/items/event_ticket.png', 1, 1, 0, 1, 1),
      ('venus_crystal', 'Venus Crystal', 'Rare crystal for Venus Board upgrades', 'currency', 'Upgrade', 'venus', 1000, 'gems', 'legendary', '/images/items/venus_crystal.png', 1, 0, 0, 0, 1)
    `);
    
    // Seed Memories
    logger.info('📝 Seeding memories...');
    await db.run(`
      INSERT OR IGNORE INTO memories (id, title, description, type, date, characters, tags, thumbnail, favorite, unlocked) VALUES
      ('beach_volleyball', 'Beach Volleyball Match', 'An exciting volleyball match on the beach', 'photo', '2024-06-01', '["kasumi", "ayane", "hitomi"]', '["sports", "beach", "competition"]', '/images/memories/beach_volleyball.jpg', 1, 1),
      ('sunset_walk', 'Sunset Beach Walk', 'A peaceful walk along the beach at sunset', 'photo', '2024-06-05', '["marie_rose", "honoka"]', '["peaceful", "sunset", "friendship"]', '/images/memories/sunset_walk.jpg', 0, 1),
      ('cooking_lesson', 'Cooking Lesson', 'Learning to cook with the girls', 'video', '2024-06-10', '["kasumi", "honoka"]', '["cooking", "learning", "fun"]', '/images/memories/cooking_lesson.jpg', 1, 1),
      ('ninja_training', 'Ninja Training', 'Secret ninja training session', 'video', '2024-06-12', '["ayane"]', '["training", "ninja", "skills"]', '/images/memories/ninja_training.jpg', 0, 1)
    `);
    
    // Seed Events
    logger.info('📝 Seeding events...');
    await db.run(`
      INSERT OR IGNORE INTO events (id, name, type, description, start_date, end_date, image, is_active, rewards, requirements) VALUES
      ('summer_festival', 'Summer Festival 2024', 'festival', 'A grand summer festival with special activities and rewards', '2024-07-01 00:00:00', '2024-07-31 23:59:59', '/images/events/summer_festival.jpg', 1, '{"coins": 10000, "gems": 500, "tickets": 50}', '{"min_level": 10, "activities": ["volleyball", "cooking"]}'),
      ('venus_gacha', 'Venus Collection Gacha', 'gacha', 'Limited gacha featuring Venus-themed swimsuits', '2024-06-15 00:00:00', '2024-06-30 23:59:59', '/images/events/venus_gacha.jpg', 1, '{"swimsuits": ["kasumi_venus"], "accessories": ["venus_crown"]}', '{"currency": "gems", "cost": 300}'),
      ('ranking_battle', 'Beach Volleyball Ranking', 'ranking', 'Compete in volleyball matches to climb the rankings', '2024-06-01 00:00:00', '2024-06-28 23:59:59', '/images/events/ranking_battle.jpg', 1, '{"trophies": true, "exclusive_swimsuits": true}', '{"min_matches": 10, "skill_level": "intermediate"}}')
    `);
    
    // Seed Bromides
    logger.info('📝 Seeding bromides...');
    await db.run(`
      INSERT OR IGNORE INTO bromides (id, name, type, rarity, description, character_id, effects, source, image) VALUES
      ('kasumi_smile', 'Kasumi Smile', 'Character', 'SR', 'A beautiful photo of Kasumi smiling', 'kasumi', '{"mood_boost": 10, "appeal_bonus": 5}', 'Photo Session', '/images/bromides/kasumi_smile.jpg'),
      ('beach_sunset', 'Beach Sunset', 'Scene', 'SSR', 'A stunning sunset scene at the beach', null, '{"atmosphere": "romantic", "mood_boost": 15}', 'Event Reward', '/images/bromides/beach_sunset.jpg'),
      ('golden_frame', 'Golden Frame', 'Frame', 'UR', 'An elegant golden frame for special photos', null, '{"rarity_boost": 20, "prestige": 10}', 'Premium Shop', '/images/bromides/golden_frame.jpg'),
      ('tropical_bg', 'Tropical Background', 'Background', 'R', 'A tropical paradise background', null, '{"theme": "tropical", "mood_boost": 3}', 'Standard Shop', '/images/bromides/tropical_bg.jpg')
    `);
    
    logger.info('✅ Data seeding completed successfully!');
    
    // Verify seeded data
    const counts = await Promise.all([
      db.get('SELECT COUNT(*) as count FROM characters'),
      db.get('SELECT COUNT(*) as count FROM skills'),
      db.get('SELECT COUNT(*) as count FROM swimsuits'),
      db.get('SELECT COUNT(*) as count FROM shop_items'),
      db.get('SELECT COUNT(*) as count FROM memories'),
      db.get('SELECT COUNT(*) as count FROM events'),
      db.get('SELECT COUNT(*) as count FROM bromides')
    ]);
    
    logger.info('📊 Seeded data summary:');
    logger.info(`  - Characters: ${counts[0].count}`);
    logger.info(`  - Skills: ${counts[1].count}`);
    logger.info(`  - Swimsuits: ${counts[2].count}`);
    logger.info(`  - Shop Items: ${counts[3].count}`);
    logger.info(`  - Memories: ${counts[4].count}`);
    logger.info(`  - Events: ${counts[5].count}`);
    logger.info(`  - Bromides: ${counts[6].count}`);
    
  } catch (error: any) {
    logger.error('❌ Data seeding failed:', error);
    throw error;
  } finally {
    if (db) {
      await db.close();
      logger.info('🔌 Database connection closed');
    }
  }
}

// Run seeding if this script is executed directly
if (import.meta.main) {
  seedData()
    .then(() => {
      logger.info('✨ Data seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Data seeding failed:', error);
      process.exit(1);
    });
}

export { seedData };
