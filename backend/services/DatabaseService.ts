import * as sql from 'mssql';
import pool, { getRequest, initializePool } from '../config/database';
import logger from '../config/logger';
import { AppError } from '../middleware/errorHandler';

// Import types from the existing types file
import {
  Character,
  Skill,
  Swimsuit,
  Girl,
  Accessory,
  VenusBoard,
  Event,
  Bromide,
  Document,
  Memory,
  ShopItem,
  NewCharacter,
  NewSkill,
  NewSwimsuit,
  NewGirl,
  NewAccessory,
  NewVenusBoard,
  NewEvent,
  NewBromide,
  NewDocument,
  NewMemory,
  NewShopItem
} from '../types/database';

interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export class DatabaseService {
  private pool: sql.ConnectionPool;

  constructor() {
    this.pool = pool;
  }

  // Initialize the database connection
  async initialize(): Promise<void> {
    await initializePool();
  }

  // Health check method
  async healthCheck(): Promise<{ status: string; timestamp: string; database: string }> {
    try {
      const request = getRequest();
      await request.query('SELECT 1 as test');
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        database: 'SQLite (SQL Server compatible)'
      };
    } catch (error) {
      throw new AppError('Database health check failed', 500);
    }
  }

  // Helper method for transactions
  async withTransaction<T>(callback: (transaction: sql.Transaction) => Promise<T>): Promise<T> {
    const transaction = new sql.Transaction(this.pool);
    try {
      await transaction.begin();
      const result = await callback(transaction);
      await transaction.commit();
      return result;
    } catch (error) {
      await transaction.rollback();
      logger.error('Transaction failed:', error);
      throw error;
    }
  }

  // Generic pagination helper for SQLite
  private async getPaginatedResults<T>(
    baseQuery: string,
    countQuery: string,
    options: PaginationOptions = {},
    mapper: (row: any) => T,
    params: any[] = []
  ): Promise<PaginatedResult<T>> {
    const { page = 1, limit = 10, sortBy = 'created_at', sortOrder = 'desc' } = options;
    const offset = (page - 1) * limit;

    try {
      // Get total count
      const countRequest = getRequest();
      const countResult = await countRequest.query(countQuery, params);
      const total = countResult.recordset[0].count || 0;

      // Get paginated data using SQLite LIMIT/OFFSET
      const dataRequest = getRequest();
      const dataQuery = `${baseQuery} ORDER BY ${sortBy} ${sortOrder.toUpperCase()} LIMIT ${limit} OFFSET ${offset}`;
      const dataResult = await dataRequest.query(dataQuery, params);

      const totalPages = Math.ceil(total / limit);

      return {
        data: dataResult.recordset.map(mapper),
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      logger.error('Pagination query failed:', error);
      throw new AppError('Failed to fetch paginated results', 500);
    }
  }

  // Characters CRUD
  async createCharacter(character: NewCharacter): Promise<Character> {
    try {
      const request = getRequest();
      request.input('id', sql.NVarChar(255), character.id);
      request.input('name', sql.NVarChar(255), character.name);
      request.input('name_jp', sql.NVarChar(255), character.nameJp);
      request.input('name_en', sql.NVarChar(255), character.nameEn);
      request.input('name_zh', sql.NVarChar(255), character.nameZh);
      request.input('rarity', sql.NVarChar(10), character.rarity);
      request.input('birthday', sql.Date, character.birthday);
      request.input('height', sql.Int, character.height);
      request.input('bust', sql.Int, character.bust);
      request.input('waist', sql.Int, character.waist);
      request.input('hip', sql.Int, character.hip);
      request.input('hobby', sql.NVarChar(sql.MAX), character.hobby);
      request.input('favorite_food', sql.NVarChar(sql.MAX), character.favoriteFood);
      request.input('description', sql.NVarChar(sql.MAX), character.description);
      request.input('avatar_image', sql.NVarChar(500), character.avatarImage);
      request.input('background_image', sql.NVarChar(500), character.backgroundImage);
      request.input('voice_actor', sql.NVarChar(255), character.voiceActor);

      const result = await request.query(
        `INSERT INTO characters (id, name, name_jp, name_en, name_zh, rarity, birthday, height, bust, waist, hip, hobby, favorite_food, description, avatar_image, background_image, voice_actor, created_at, updated_at)
         OUTPUT INSERTED.*
         VALUES (@id, @name, @name_jp, @name_en, @name_zh, @rarity, @birthday, @height, @bust, @waist, @hip, @hobby, @favorite_food, @description, @avatar_image, @background_image, @voice_actor, GETDATE(), GETDATE())`
      );
      return this.mapCharacterRow(result.recordset[0]);
    } catch (error: any) {
      if (error.number === 2627) { // Unique constraint violation in SQL Server
        throw new AppError('Character with this ID already exists', 409);
      }
      throw new AppError('Failed to create character', 500);
    }
  }

  async getCharacters(options: PaginationOptions = {}): Promise<PaginatedResult<Character>> {
    return this.getPaginatedResults(
      'SELECT * FROM characters',
      'SELECT COUNT(*) as count FROM characters',
      options,
      this.mapCharacterRow
    );
  }

  async getCharacterById(id: string): Promise<Character> {
    const request = getRequest();
    request.input('id', sql.NVarChar(255), id);
    const result = await request.query('SELECT * FROM characters WHERE id = @id');
    if (result.recordset.length === 0) {
      throw new AppError('Character not found', 404);
    }
    return this.mapCharacterRow(result.recordset[0]);
  }

  async updateCharacter(id: string, updates: Partial<NewCharacter>): Promise<Character> {
    const setClause: string[] = [];
    const request = getRequest();
    request.input('id', sql.NVarChar(255), id);

    if (updates.name !== undefined) {
      setClause.push(`name = @name`);
      request.input('name', sql.NVarChar(255), updates.name);
    }
    if (updates.nameJp !== undefined) {
      setClause.push(`name_jp = @name_jp`);
      request.input('name_jp', sql.NVarChar(255), updates.nameJp);
    }
    if (updates.nameEn !== undefined) {
      setClause.push(`name_en = @name_en`);
      request.input('name_en', sql.NVarChar(255), updates.nameEn);
    }
    if (updates.nameZh !== undefined) {
      setClause.push(`name_zh = @name_zh`);
      request.input('name_zh', sql.NVarChar(255), updates.nameZh);
    }
    if (updates.rarity !== undefined) {
      setClause.push(`rarity = @rarity`);
      request.input('rarity', sql.NVarChar(10), updates.rarity);
    }
    if (updates.birthday !== undefined) {
      setClause.push(`birthday = @birthday`);
      request.input('birthday', sql.Date, updates.birthday);
    }
    if (updates.height !== undefined) {
      setClause.push(`height = @height`);
      request.input('height', sql.Int, updates.height);
    }
    if (updates.bust !== undefined) {
      setClause.push(`bust = @bust`);
      request.input('bust', sql.Int, updates.bust);
    }
    if (updates.waist !== undefined) {
      setClause.push(`waist = @waist`);
      request.input('waist', sql.Int, updates.waist);
    }
    if (updates.hip !== undefined) {
      setClause.push(`hip = @hip`);
      request.input('hip', sql.Int, updates.hip);
    }
    if (updates.hobby !== undefined) {
      setClause.push(`hobby = @hobby`);
      request.input('hobby', sql.NVarChar(sql.MAX), updates.hobby);
    }
    if (updates.favoriteFood !== undefined) {
      setClause.push(`favorite_food = @favorite_food`);
      request.input('favorite_food', sql.NVarChar(sql.MAX), updates.favoriteFood);
    }
    if (updates.description !== undefined) {
      setClause.push(`description = @description`);
      request.input('description', sql.NVarChar(sql.MAX), updates.description);
    }
    if (updates.avatarImage !== undefined) {
      setClause.push(`avatar_image = @avatar_image`);
      request.input('avatar_image', sql.NVarChar(500), updates.avatarImage);
    }
    if (updates.backgroundImage !== undefined) {
      setClause.push(`background_image = @background_image`);
      request.input('background_image', sql.NVarChar(500), updates.backgroundImage);
    }
    if (updates.voiceActor !== undefined) {
      setClause.push(`voice_actor = @voice_actor`);
      request.input('voice_actor', sql.NVarChar(255), updates.voiceActor);
    }

    if (setClause.length === 0) {
      return this.getCharacterById(id);
    }

    setClause.push(`updated_at = GETDATE()`);

    const result = await request.query(
      `UPDATE characters SET ${setClause.join(', ')} OUTPUT INSERTED.* WHERE id = @id`
    );

    if (result.recordset.length === 0) {
      throw new AppError('Character not found', 404);
    }

    return this.mapCharacterRow(result.recordset[0]);
  }

  async deleteCharacter(id: string): Promise<void> {
    const request = getRequest();
    request.input('id', sql.NVarChar(255), id);
    const result = await request.query('DELETE FROM characters WHERE id = @id');
    if (result.rowsAffected[0] === 0) {
      throw new AppError('Character not found', 404);
    }
  }

  // Skills CRUD
  async createSkill(skill: NewSkill): Promise<Skill> {
    try {
      const request = getRequest();
      request.input('id', sql.NVarChar(255), skill.id);
      request.input('name', sql.NVarChar(255), skill.name);
      request.input('type', sql.NVarChar(100), skill.type);
      request.input('category', sql.NVarChar(100), skill.category || 'general');
      request.input('description', sql.NVarChar(sql.MAX), skill.description);
      request.input('icon', sql.NVarChar(255), skill.icon);
      request.input('max_level', sql.Int, skill.maxLevel || 1);
      request.input('effect_type', sql.NVarChar(100), skill.effectType);
      request.input('effect_value', sql.Decimal(5, 2), skill.effectValue);
      request.input('cooldown', sql.Int, skill.cooldown || 0);
      request.input('duration', sql.Int, skill.duration || 0);

      const result = await request.query(
        `INSERT INTO skills (id, name, type, category, description, icon, max_level, effect_type, effect_value, cooldown, duration, created_at, updated_at)
         OUTPUT INSERTED.*
         VALUES (@id, @name, @type, @category, @description, @icon, @max_level, @effect_type, @effect_value, @cooldown, @duration, GETDATE(), GETDATE())`
      );
      return this.mapSkillRow(result.recordset[0]);
    } catch (error: any) {
      if (error.number === 2627) {
        throw new AppError('Skill with this ID already exists', 409);
      }
      throw new AppError('Failed to create skill', 500);
    }
  }

  async getSkills(options: PaginationOptions = {}): Promise<PaginatedResult<Skill>> {
    return this.getPaginatedResults(
      'SELECT * FROM skills',
      'SELECT COUNT(*) as count FROM skills',
      options,
      this.mapSkillRow
    );
  }

  async getSkillById(id: string): Promise<Skill> {
    const request = getRequest();
    request.input('id', sql.NVarChar(255), id);
    const result = await request.query('SELECT * FROM skills WHERE id = @id');
    if (result.recordset.length === 0) {
      throw new AppError('Skill not found', 404);
    }
    return this.mapSkillRow(result.recordset[0]);
  }

  async updateSkill(id: string, updates: Partial<NewSkill>): Promise<Skill> {
    const setClause: string[] = [];
    const request = getRequest();
    request.input('id', sql.NVarChar(255), id);

    if (updates.name !== undefined) {
      setClause.push(`name = @name`);
      request.input('name', sql.NVarChar(255), updates.name);
    }
    if (updates.type !== undefined) {
      setClause.push(`type = @type`);
      request.input('type', sql.NVarChar(100), updates.type);
    }
    if (updates.category !== undefined) {
      setClause.push(`category = @category`);
      request.input('category', sql.NVarChar(100), updates.category);
    }
    if (updates.description !== undefined) {
      setClause.push(`description = @description`);
      request.input('description', sql.NVarChar(sql.MAX), updates.description);
    }
    if (updates.icon !== undefined) {
      setClause.push(`icon = @icon`);
      request.input('icon', sql.NVarChar(255), updates.icon);
    }
    if (updates.maxLevel !== undefined) {
      setClause.push(`max_level = @max_level`);
      request.input('max_level', sql.Int, updates.maxLevel);
    }
    if (updates.effectType !== undefined) {
      setClause.push(`effect_type = @effect_type`);
      request.input('effect_type', sql.NVarChar(100), updates.effectType);
    }
    if (updates.effectValue !== undefined) {
      setClause.push(`effect_value = @effect_value`);
      request.input('effect_value', sql.Decimal(5, 2), updates.effectValue);
    }
    if (updates.cooldown !== undefined) {
      setClause.push(`cooldown = @cooldown`);
      request.input('cooldown', sql.Int, updates.cooldown);
    }
    if (updates.duration !== undefined) {
      setClause.push(`duration = @duration`);
      request.input('duration', sql.Int, updates.duration);
    }

    if (setClause.length === 0) {
      return this.getSkillById(id);
    }

    setClause.push(`updated_at = GETDATE()`);

    const result = await request.query(
      `UPDATE skills SET ${setClause.join(', ')} OUTPUT INSERTED.* WHERE id = @id`
    );

    if (result.recordset.length === 0) {
      throw new AppError('Skill not found', 404);
    }

    return this.mapSkillRow(result.recordset[0]);
  }

  async deleteSkill(id: string): Promise<void> {
    const request = getRequest();
    request.input('id', sql.NVarChar(255), id);
    const result = await request.query('DELETE FROM skills WHERE id = @id');
    if (result.rowsAffected[0] === 0) {
      throw new AppError('Skill not found', 404);
    }
  }

  // Row mapping functions
  private mapCharacterRow(row: any): Character {
    return {
      id: row.id,
      name: row.name,
      nameJp: row.name_jp,
      nameEn: row.name_en,
      nameZh: row.name_zh,
      rarity: row.rarity,
      birthday: row.birthday,
      height: row.height,
      bust: row.bust,
      waist: row.waist,
      hip: row.hip,
      hobby: row.hobby,
      favoriteFood: row.favorite_food,
      description: row.description,
      avatarImage: row.avatar_image,
      backgroundImage: row.background_image,
      voiceActor: row.voice_actor,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }

  private mapSkillRow(row: any): Skill {
    return {
      id: row.id,
      name: row.name,
      type: row.type,
      category: row.category,
      description: row.description,
      icon: row.icon,
      maxLevel: row.max_level,
      effectType: row.effect_type,
      effectValue: row.effect_value,
      cooldown: row.cooldown,
      duration: row.duration,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }

  // Memories CRUD
  async createMemory(memory: NewMemory): Promise<Memory> {
    try {
      const request = getRequest();
      request.input('id', memory.id);
      request.input('title', memory.title);
      request.input('description', memory.description);
      request.input('type', memory.type);
      request.input('date', memory.date);
      request.input('characters', JSON.stringify(memory.characters || []));
      request.input('tags', JSON.stringify(memory.tags || []));
      request.input('thumbnail', memory.thumbnail);
      request.input('favorite', memory.favorite ? 1 : 0);
      request.input('unlocked', memory.unlocked ? 1 : 0);

      const result = await request.query(
        `INSERT INTO memories (id, title, description, type, date, characters, tags, thumbnail, favorite, unlocked, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
         RETURNING *`,
        [memory.id, memory.title, memory.description, memory.type, memory.date,
         JSON.stringify(memory.characters || []), JSON.stringify(memory.tags || []),
         memory.thumbnail, memory.favorite ? 1 : 0, memory.unlocked ? 1 : 0]
      );
      return this.mapMemoryRow(result.recordset[0]);
    } catch (error: any) {
      throw new AppError('Failed to create memory', 500);
    }
  }

  async getMemories(options: PaginationOptions = {}): Promise<PaginatedResult<Memory>> {
    return this.getPaginatedResults(
      'SELECT * FROM memories',
      'SELECT COUNT(*) as count FROM memories',
      options,
      this.mapMemoryRow
    );
  }

  async getMemoryById(id: string): Promise<Memory> {
    const request = getRequest();
    const result = await request.query('SELECT * FROM memories WHERE id = ?', [id]);
    if (result.recordset.length === 0) {
      throw new AppError('Memory not found', 404);
    }
    return this.mapMemoryRow(result.recordset[0]);
  }

  async updateMemory(id: string, updates: Partial<NewMemory>): Promise<Memory> {
    const setClause: string[] = [];
    const params: any[] = [];

    if (updates.title !== undefined) {
      setClause.push(`title = ?`);
      params.push(updates.title);
    }
    if (updates.description !== undefined) {
      setClause.push(`description = ?`);
      params.push(updates.description);
    }
    if (updates.type !== undefined) {
      setClause.push(`type = ?`);
      params.push(updates.type);
    }
    if (updates.date !== undefined) {
      setClause.push(`date = ?`);
      params.push(updates.date);
    }
    if (updates.characters !== undefined) {
      setClause.push(`characters = ?`);
      params.push(JSON.stringify(updates.characters));
    }
    if (updates.tags !== undefined) {
      setClause.push(`tags = ?`);
      params.push(JSON.stringify(updates.tags));
    }
    if (updates.thumbnail !== undefined) {
      setClause.push(`thumbnail = ?`);
      params.push(updates.thumbnail);
    }
    if (updates.favorite !== undefined) {
      setClause.push(`favorite = ?`);
      params.push(updates.favorite ? 1 : 0);
    }
    if (updates.unlocked !== undefined) {
      setClause.push(`unlocked = ?`);
      params.push(updates.unlocked ? 1 : 0);
    }

    if (setClause.length === 0) {
      return this.getMemoryById(id);
    }

    setClause.push(`updated_at = datetime('now')`);
    params.push(id);

    const request = getRequest();
    const result = await request.query(
      `UPDATE memories SET ${setClause.join(', ')} WHERE id = ? RETURNING *`,
      params
    );

    if (result.recordset.length === 0) {
      throw new AppError('Memory not found', 404);
    }

    return this.mapMemoryRow(result.recordset[0]);
  }

  async deleteMemory(id: string): Promise<void> {
    const request = getRequest();
    const result = await request.query('DELETE FROM memories WHERE id = ?', [id]);
    if (result.rowsAffected[0] === 0) {
      throw new AppError('Memory not found', 404);
    }
  }

  async getFavoriteMemories(options: PaginationOptions = {}): Promise<PaginatedResult<Memory>> {
    return this.getPaginatedResults(
      'SELECT * FROM memories WHERE favorite = 1',
      'SELECT COUNT(*) as count FROM memories WHERE favorite = 1',
      options,
      this.mapMemoryRow
    );
  }

  async getMemoriesByType(type: 'photo' | 'video', options: PaginationOptions = {}): Promise<PaginatedResult<Memory>> {
    return this.getPaginatedResults(
      'SELECT * FROM memories WHERE type = ?',
      'SELECT COUNT(*) as count FROM memories WHERE type = ?',
      options,
      this.mapMemoryRow,
      [type]
    );
  }

  private mapMemoryRow(row: any): Memory {
    return {
      id: row.id,
      title: row.title,
      description: row.description,
      type: row.type,
      date: row.date,
      characters: row.characters ? JSON.parse(row.characters) : [],
      tags: row.tags ? JSON.parse(row.tags) : [],
      thumbnail: row.thumbnail,
      favorite: Boolean(row.favorite),
      unlocked: Boolean(row.unlocked),
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }

  // Shop Items CRUD
  async createShopItem(shopItem: NewShopItem): Promise<ShopItem> {
    try {
      const request = getRequest();
      const result = await request.query(
        `INSERT INTO shop_items (id, name, description, type, category, section, price, currency, rarity, image, in_stock, is_new, discount, limited_time, featured, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
         RETURNING *`,
        [shopItem.id, shopItem.name, shopItem.description, shopItem.type, shopItem.category,
         shopItem.section, shopItem.price, shopItem.currency, shopItem.rarity, shopItem.image,
         shopItem.inStock ? 1 : 0, shopItem.isNew ? 1 : 0, shopItem.discount || 0,
         shopItem.limitedTime ? 1 : 0, shopItem.featured ? 1 : 0]
      );
      return this.mapShopItemRow(result.recordset[0]);
    } catch (error: any) {
      throw new AppError('Failed to create shop item', 500);
    }
  }

  async getShopItems(options: PaginationOptions = {}, filters: any = {}): Promise<PaginatedResult<ShopItem>> {
    let whereClause = '';
    const params: any[] = [];

    if (filters.section) {
      whereClause += ' WHERE section = ?';
      params.push(filters.section);
    }
    if (filters.type) {
      whereClause += whereClause ? ' AND type = ?' : ' WHERE type = ?';
      params.push(filters.type);
    }
    if (filters.rarity) {
      whereClause += whereClause ? ' AND rarity = ?' : ' WHERE rarity = ?';
      params.push(filters.rarity);
    }
    if (filters.inStock !== undefined) {
      whereClause += whereClause ? ' AND in_stock = ?' : ' WHERE in_stock = ?';
      params.push(filters.inStock ? 1 : 0);
    }
    if (filters.featured !== undefined) {
      whereClause += whereClause ? ' AND featured = ?' : ' WHERE featured = ?';
      params.push(filters.featured ? 1 : 0);
    }

    return this.getPaginatedResults(
      `SELECT * FROM shop_items${whereClause}`,
      `SELECT COUNT(*) as count FROM shop_items${whereClause}`,
      options,
      this.mapShopItemRow,
      params
    );
  }

  async getShopItemById(id: string): Promise<ShopItem> {
    const request = getRequest();
    const result = await request.query('SELECT * FROM shop_items WHERE id = ?', [id]);
    if (result.recordset.length === 0) {
      throw new AppError('Shop item not found', 404);
    }
    return this.mapShopItemRow(result.recordset[0]);
  }

  async updateShopItem(id: string, updates: Partial<NewShopItem>): Promise<ShopItem> {
    const setClause: string[] = [];
    const params: any[] = [];

    if (updates.name !== undefined) {
      setClause.push(`name = ?`);
      params.push(updates.name);
    }
    if (updates.description !== undefined) {
      setClause.push(`description = ?`);
      params.push(updates.description);
    }
    if (updates.type !== undefined) {
      setClause.push(`type = ?`);
      params.push(updates.type);
    }
    if (updates.category !== undefined) {
      setClause.push(`category = ?`);
      params.push(updates.category);
    }
    if (updates.section !== undefined) {
      setClause.push(`section = ?`);
      params.push(updates.section);
    }
    if (updates.price !== undefined) {
      setClause.push(`price = ?`);
      params.push(updates.price);
    }
    if (updates.currency !== undefined) {
      setClause.push(`currency = ?`);
      params.push(updates.currency);
    }
    if (updates.rarity !== undefined) {
      setClause.push(`rarity = ?`);
      params.push(updates.rarity);
    }
    if (updates.image !== undefined) {
      setClause.push(`image = ?`);
      params.push(updates.image);
    }
    if (updates.inStock !== undefined) {
      setClause.push(`in_stock = ?`);
      params.push(updates.inStock ? 1 : 0);
    }
    if (updates.isNew !== undefined) {
      setClause.push(`is_new = ?`);
      params.push(updates.isNew ? 1 : 0);
    }
    if (updates.discount !== undefined) {
      setClause.push(`discount = ?`);
      params.push(updates.discount);
    }
    if (updates.limitedTime !== undefined) {
      setClause.push(`limited_time = ?`);
      params.push(updates.limitedTime ? 1 : 0);
    }
    if (updates.featured !== undefined) {
      setClause.push(`featured = ?`);
      params.push(updates.featured ? 1 : 0);
    }

    if (setClause.length === 0) {
      return this.getShopItemById(id);
    }

    setClause.push(`updated_at = datetime('now')`);
    params.push(id);

    const request = getRequest();
    const result = await request.query(
      `UPDATE shop_items SET ${setClause.join(', ')} WHERE id = ? RETURNING *`,
      params
    );

    if (result.recordset.length === 0) {
      throw new AppError('Shop item not found', 404);
    }

    return this.mapShopItemRow(result.recordset[0]);
  }

  async deleteShopItem(id: string): Promise<void> {
    const request = getRequest();
    const result = await request.query('DELETE FROM shop_items WHERE id = ?', [id]);
    if (result.rowsAffected[0] === 0) {
      throw new AppError('Shop item not found', 404);
    }
  }

  async getShopItemsBySection(section: string, options: PaginationOptions = {}): Promise<PaginatedResult<ShopItem>> {
    return this.getPaginatedResults(
      'SELECT * FROM shop_items WHERE section = ?',
      'SELECT COUNT(*) as count FROM shop_items WHERE section = ?',
      options,
      this.mapShopItemRow,
      [section]
    );
  }

  async getFeaturedShopItems(options: PaginationOptions = {}): Promise<PaginatedResult<ShopItem>> {
    return this.getPaginatedResults(
      'SELECT * FROM shop_items WHERE featured = 1',
      'SELECT COUNT(*) as count FROM shop_items WHERE featured = 1',
      options,
      this.mapShopItemRow
    );
  }

  private mapShopItemRow(row: any): ShopItem {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      type: row.type,
      category: row.category,
      section: row.section,
      price: row.price,
      currency: row.currency,
      rarity: row.rarity,
      image: row.image,
      inStock: Boolean(row.in_stock),
      isNew: Boolean(row.is_new),
      discount: row.discount,
      limitedTime: Boolean(row.limited_time),
      featured: Boolean(row.featured),
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }
}

// Create and export a singleton instance
const databaseService = new DatabaseService();
export default databaseService;
