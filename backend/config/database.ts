import sqlite3 from 'sqlite3';
import { open, Database } from 'sqlite';
import { join } from 'path';
import logger from './logger';

export interface DatabaseConfig {
  filename: string;
  driver: typeof sqlite3.Database;
}

const config: DatabaseConfig = {
  filename: join(__dirname, '../data/doaxvv_handbook.db'),
  driver: sqlite3.Database
};

let db: Database<sqlite3.Database, sqlite3.Statement> | null = null;

// Initialize SQLite database
export async function initializePool(): Promise<void> {
  try {
    db = await open(config);
    
    // Enable foreign keys
    await db.exec('PRAGMA foreign_keys = ON');
    
    logger.info('SQLite database connection initialized');
  } catch (error) {
    logger.error('Failed to initialize SQLite database:', error);
    throw error;
  }
}

// Test database connection
export async function testConnection(): Promise<boolean> {
  try {
    if (!db) {
      await initializePool();
    }
    await db!.get('SELECT 1 as test');
    logger.info('Database connection successful');
    return true;
  } catch (error) {
    logger.error('Database connection failed:', error);
    return false;
  }
}

// Graceful shutdown
export async function closeDatabase(): Promise<void> {
  try {
    if (db) {
      await db.close();
      db = null;
      logger.info('Database connection closed');
    }
  } catch (error) {
    logger.error('Error closing database:', error);
  }
}

// Get database instance
export function getDatabase(): Database<sqlite3.Database, sqlite3.Statement> {
  if (!db) {
    throw new Error('Database not initialized. Call initializePool() first.');
  }
  return db;
}

// SQL Server to SQLite query adapter
export class SQLiteAdapter {
  private db: Database<sqlite3.Database, sqlite3.Statement>;

  constructor(database: Database<sqlite3.Database, sqlite3.Statement>) {
    this.db = database;
  }

  // Convert SQL Server syntax to SQLite
  private adaptQuery(query: string): string {
    return query
      // Convert NVARCHAR to TEXT
      .replace(/NVARCHAR\(MAX\)/gi, 'TEXT')
      .replace(/NVARCHAR\(\d+\)/gi, 'TEXT')
      // Convert DATETIME2 to DATETIME
      .replace(/DATETIME2/gi, 'DATETIME')
      // Convert GETDATE() to datetime('now')
      .replace(/GETDATE\(\)/gi, "datetime('now')")
      // Convert BIT to INTEGER (0/1)
      .replace(/BIT/gi, 'INTEGER')
      // Convert IDENTITY to AUTOINCREMENT
      .replace(/INT IDENTITY\(1,1\)/gi, 'INTEGER PRIMARY KEY AUTOINCREMENT')
      .replace(/IDENTITY\(1,1\)/gi, 'AUTOINCREMENT')
      // Convert OUTPUT INSERTED.* to RETURNING *
      .replace(/OUTPUT INSERTED\.\*/gi, 'RETURNING *')
      // Convert IF OBJECT_ID to IF EXISTS
      .replace(/IF OBJECT_ID\('([^']+)', 'U'\) IS NOT NULL DROP TABLE ([^;]+);/gi, 'DROP TABLE IF EXISTS $2;')
      // Convert parameter syntax from @param to ?
      .replace(/@(\w+)/g, '?');
  }

  // Execute query with parameter adaptation
  async query(sql: string, params: any[] = []): Promise<any> {
    const adaptedQuery = this.adaptQuery(sql);

    try {
      // Handle different query types
      if (adaptedQuery.trim().toUpperCase().startsWith('SELECT')) {
        const result = await this.db.all(adaptedQuery, params);
        return { recordset: result, rowsAffected: [result.length] };
      } else if (adaptedQuery.trim().toUpperCase().startsWith('INSERT') && adaptedQuery.includes('RETURNING')) {
        // SQLite doesn't support RETURNING in the same way, so we need to handle this differently
        const insertQuery = adaptedQuery.replace(/\s+RETURNING\s+\*/i, '');
        const result = await this.db.run(insertQuery, params);
        if (result.lastID) {
          // Get the inserted record
          const selectResult = await this.db.get('SELECT * FROM ' + this.extractTableName(insertQuery) + ' WHERE rowid = ?', [result.lastID]);
          return { recordset: [selectResult], rowsAffected: [1] };
        }
        return { recordset: [], rowsAffected: [result.changes || 0] };
      } else if (adaptedQuery.trim().toUpperCase().startsWith('UPDATE') && adaptedQuery.includes('RETURNING')) {
        // Handle UPDATE with RETURNING
        const updateQuery = adaptedQuery.replace(/\s+RETURNING\s+\*/i, '');
        const result = await this.db.run(updateQuery, params);
        return { recordset: [], rowsAffected: [result.changes || 0] };
      } else {
        const result = await this.db.run(adaptedQuery, params);
        return { recordset: [], rowsAffected: [result.changes || 0] };
      }
    } catch (error) {
      logger.error('SQLite query failed:', { query: adaptedQuery, params, error });
      throw error;
    }
  }

  // Helper method to extract table name from INSERT query
  private extractTableName(query: string): string {
    const match = query.match(/INSERT\s+INTO\s+(\w+)/i);
    return match ? match[1] : '';
  }

  // Begin transaction
  async begin(): Promise<void> {
    await this.db.exec('BEGIN TRANSACTION');
  }

  // Commit transaction
  async commit(): Promise<void> {
    await this.db.exec('COMMIT');
  }

  // Rollback transaction
  async rollback(): Promise<void> {
    await this.db.exec('ROLLBACK');
  }

  // Input method for parameter binding (SQL Server compatibility)
  input(name: string, type: any, value: any): void {
    // SQLite doesn't need explicit parameter typing
    // This method exists for SQL Server compatibility
  }
}

// Create request-like object for SQL Server compatibility
export function getRequest(): SQLiteAdapter {
  const database = getDatabase();
  return new SQLiteAdapter(database);
}

// Export database instance
export default getDatabase;
